{"cells": [{"cell_type": "markdown", "id": "633bfa0b", "metadata": {}, "source": ["## Initial Transformation and Data Cleaning"]}, {"cell_type": "code", "execution_count": 1, "id": "3948e5ef", "metadata": {}, "outputs": [], "source": ["import time\n", "start_time = time.time()"]}, {"cell_type": "code", "execution_count": 4, "id": "b2b4ab5b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["⚠️  GPU acceleration failed: No module named 'cudf'\n", "📊 Using standard pandas\n", "\n", "🔧 Backend Configuration Summary:\n", "   Primary backend: pandas\n", "   GPU acceleration: ❌\n", "   Modin distributed: ❌\n", "   Warnings: 1\n", "     - GPU setup failed: No module named 'cudf'\n"]}], "source": ["# Importing custom functions \n", "import useful_functions as uf\n", "\n", "USE_GPU = True\n", "USE_MODIN = False\n", "config = uf.configure_dataframe_backend(use_gpu=USE_GPU, use_modin=USE_MODIN)\n"]}, {"cell_type": "code", "execution_count": null, "id": "05d936fd", "metadata": {}, "outputs": [], "source": ["# Optional GPU acceleration: enable cuDF's pandas accelerator if available (comment out if no CUDA)\n", "# https://developer.nvidia.com/blog/7-drop-in-replacements-to-instantly-speed-up-your-python-data-science-workflows/\n", "\n", "# Importing custom functions \n", "import useful_functions as uf\n", "\n", "USE_GPU = True\n", "USE_MODIN = False\n", "\n", "if USE_GPU:\n", "    try:\n", "        get_ipython().run_line_magic('load_ext', 'cudf.pandas')\n", "        import cupy as cp, cudf\n", "        print(\"cuDF:\", cudf.__version__, \" | CuPy:\", cp.__version__)\n", "        n = cp.cuda.runtime.getDeviceCount()\n", "        print(\"GPU count:\", n)\n", "        for i in range(n):\n", "            p = cp.cuda.runtime.getDeviceProperties(i)\n", "        print(f\"[{i}] {p['name'].decode()} - {p['totalGlobalMem']//(1024**3)} GB\")\n", "        print(\"GPU acceleration enabled\") \n", "        GPU_ACCEL = True\n", "    except Exception:\n", "        print(\"GPU acceleration disabled\") \n", "        GPU_ACCEL = False   \n", "        try:\n", "            if USE_MODIN:\n", "                import modin.pandas as pd\n", "                MODIN_AVAILABLE = True\n", "                print(\"Modin available, using Mo<PERSON> with Das<PERSON> backend.\")\n", "                uf.configure_modin_settings()\n", "            else:\n", "                import pandas as pd\n", "                MODIN_AVAILABLE = False\n", "                print(\"Modin not available, using pandas.\")\n", "        except ImportError:\n", "            import pandas as pd\n", "            MODIN_AVAILABLE = False\n", "            print(\"Modin not available, using pandas.\")\n", "print(\"Libraries imported successfully\")"]}, {"cell_type": "code", "execution_count": null, "id": "47185e9d", "metadata": {}, "outputs": [], "source": ["import os\n", "CUSTOMERS_FILE = 'data/customers.csv'\n", "if not os.path.exists(CUSTOMERS_FILE):\n", "    print(f\"File not found: {CUSTOMERS_FILE}\")\n", "else:\n", "    try:\n", "        customers_df = pd.read_csv(CUSTOMERS_FILE, delimiter='|')\n", "        uf.show_df_info(customers_df)\n", "    except Exception as e:\n", "        print(f\"Error loading {CUSTOMERS_FILE}: {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "ac5634ef", "metadata": {}, "outputs": [], "source": ["# Splitting the profile column into multiple columns\n", "# https://pandas.pydata.org/docs/reference/api/pandas.Series.str.rsplit.html\n", "\n", "profile_parts = customers_df['profile'].str.rsplit('_', n=2, expand=True)\n", "profile_parts.head()"]}, {"cell_type": "code", "execution_count": null, "id": "539343be", "metadata": {}, "outputs": [], "source": ["# Leveraging the information from the profile column to create new columns(Feature Engineering)\n", "\n", "customers_df['pop_group'] = profile_parts[0]\n", "customers_df['location'] = profile_parts[2].str.split('.').str[0] # Remove .json extension\n", "\n", "# Drop the original profile column\n", "customers_df.drop(columns=['profile'], inplace=True)\n", "\n", "del profile_parts # Free memory\n", "\n", "customers_df.head()"]}, {"cell_type": "markdown", "id": "974cf5a2", "metadata": {}, "source": ["####  Planning to use as less memory as possible, some of the data types will be optimized\n"]}, {"cell_type": "markdown", "id": "50b79e0c", "metadata": {}, "source": ["#### Introducing this code after discussing the project. Will play with customized functions.\n", "\n", "https://stackoverflow.com/questions/57856010/automatically-optimizing-pandas-dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "c8b8b61c", "metadata": {}, "outputs": [], "source": ["# Testing AI functions to optimize the dataframe memory usage (will use custom approach anyway)\n", "customers_df_test, optimization_success = uf.test_ai_optimization_safe(customers_df, \"customers_df\")\n", "\n", "if optimization_success:\n", "    print(\"✅ AI optimization completed successfully\")\n", "else:\n", "    print(\"⚠️ AI optimization failed, using original DataFrame\")"]}, {"cell_type": "markdown", "id": "beac22cb", "metadata": {}, "source": ["#### End of the test with AI generated funcions"]}, {"cell_type": "code", "execution_count": null, "id": "9827c432", "metadata": {}, "outputs": [], "source": ["# Display unique values in columns that might be used as categorical\n", "customers_df[['gender', 'state', 'job', 'pop_group','location']].nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "2759a2b6", "metadata": {}, "outputs": [], "source": ["# Check what type city_pop should use\n", "city_pop_recommended_type = uf.get_safe_int_type(customers_df['city_pop'])\n", "print(f\"Recommended type for city_pop: {city_pop_recommended_type}\")"]}, {"cell_type": "code", "execution_count": null, "id": "58cd07f1", "metadata": {}, "outputs": [], "source": ["# Before optimization\n", "print(\"Before optimization:\")\n", "old_mem_usage = customers_df.memory_usage(deep=True).sum() / 1024\n", "customers_df.info(memory_usage='deep')\n", "\n", "# Apply optimization\n", "new_customer_types = {\n", "    'category': ['gender', 'state', 'pop_group', 'location'],\n", "    'string': ['ssn', 'cc_num', 'first', 'last', 'street', 'city', 'dob', 'job', 'zip', 'acct_num'],\n", "    city_pop_recommended_type: ['city_pop'],\n", "    'float32': ['lat', 'long'] # Acceptable loss for fraud detection. It might need to be float64 for other applications\n", "}\n", "# Replacing customers_df with the optimized version to save memory\n", "customers_df = uf.optimize_df_types(customers_df, new_customer_types)\n", "\n", "# After optimization  \n", "\n", "new_mem_usage = customers_df.memory_usage(deep=True).sum() / 1024\n", "print(\"\\nAfter optimization:\")\n", "customers_df.info(memory_usage='deep')"]}, {"cell_type": "code", "execution_count": null, "id": "cb10635b", "metadata": {}, "outputs": [], "source": ["# Compare memory usage\n", "\n", "print(f\"Original memory usage: {old_mem_usage:,.2f} KB, optimized memory usage: {new_mem_usage:,.2f} KB\")\n", "\n", "print(f\"\\nMemory usage reduction: {100*(old_mem_usage - new_mem_usage)/old_mem_usage:.2f}%\")"]}, {"cell_type": "markdown", "id": "3bcc4f89", "metadata": {}, "source": ["##### Not a huge saving, but every bit helps! This principle might be useful when working with larger datasets."]}, {"cell_type": "markdown", "id": "657ffa69", "metadata": {}, "source": ["#### As expected, the majority of the customers are from urban areas. Adults over 50 are the majority on our small customer dataset."]}, {"cell_type": "markdown", "id": "d8f3277a", "metadata": {}, "source": ["### We will work now with the transaction files"]}, {"cell_type": "code", "execution_count": null, "id": "232f1f61", "metadata": {}, "outputs": [], "source": ["# The transaction data is split across multiple files, so we need to load them all and concatenate them into a single DataFrame\n", "transaction_files = uf.get_files_dir('data', file_mask='*.csv')\n", "transaction_files = [f for f in transaction_files if 'customers.csv' not in f] # Exclude customers.csv"]}, {"cell_type": "code", "execution_count": null, "id": "3c6c7a8e", "metadata": {}, "outputs": [], "source": ["sample_transaction_file = transaction_files[0]\n", "sample_transactions_df = pd.read_csv(sample_transaction_file, delimiter='|')\n", "uf.show_df_info(sample_transactions_df)"]}, {"cell_type": "markdown", "id": "3f83c225", "metadata": {}, "source": ["#### Unix time\n", "##### https://en.wikipedia.org/wiki/Unix_time"]}, {"cell_type": "code", "execution_count": null, "id": "4cf54e52", "metadata": {}, "outputs": [], "source": ["sample_transactions_df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "32f6c858", "metadata": {}, "outputs": [], "source": ["# Working with the transaction data \n", "# Optimizing types\n", "transaction_types = {\n", "    'category': ['category'],\n", "    'uint32': ['unix_time'], # uint32 max value is 2,147,483,647, which is more than enough for our purposes(until 2038, at least). \n", "    'float32': ['amt', 'merch_lat', 'merch_long'],\n", "    'uint8': ['is_fraud'],\n", "    'string': ['ssn', 'trans_num', 'trans_date', 'trans_time', 'merchant']\n", "}\n", "# Process and merge all transaction files\n", "transactions_df = uf.process_and_merge_files(transaction_files, transaction_types)"]}, {"cell_type": "code", "execution_count": null, "id": "f1451cd2", "metadata": {}, "outputs": [], "source": ["# Display the final merged DataFrame\n", "uf.show_df_info(transactions_df)"]}, {"cell_type": "code", "execution_count": null, "id": "873a84ef", "metadata": {}, "outputs": [], "source": ["help(uf.get_files_dir)"]}, {"cell_type": "code", "execution_count": null, "id": "ec053533", "metadata": {}, "outputs": [], "source": ["# 1. <PERSON><PERSON><PERSON> primary key candidates\n", "customer_pk_check = uf.check_primary_key_candidates(customers_df, ['ssn', 'cc_num'])\n", "transaction_pk_check = uf.check_primary_key_candidates(transactions_df, ['trans_num'])"]}, {"cell_type": "code", "execution_count": null, "id": "5b8eeaaf", "metadata": {}, "outputs": [], "source": ["print(\"Customer Primary Key Analysis:\")\n", "uf.display_primary_key_analysis( customer_pk_check)\n", "print(\"\\nTransaction Primary Key Analysis:\")\n", "uf.display_primary_key_analysis( transaction_pk_check)"]}, {"cell_type": "code", "execution_count": null, "id": "3796f5bc", "metadata": {}, "outputs": [], "source": ["# Create database\n", "# Remove the comment to create the database\n", "#uf.create_fraud_detection_db(customers_df, transactions_df)"]}, {"cell_type": "code", "execution_count": null, "id": "a3688e45", "metadata": {}, "outputs": [], "source": ["# Memory cleanup\n", "import gc\n", "\n", "# Show current memory usage\n", "customers_memory_mb = customers_df.memory_usage(deep=True).sum() / (1024 * 1024)\n", "transactions_memory_mb = transactions_df.memory_usage(deep=True).sum() / (1024 * 1024)\n", "sample_memory_mb = sample_transactions_df.memory_usage(deep=True).sum() / (1024 * 1024)\n", "total_memory_mb = customers_memory_mb + transactions_memory_mb + sample_memory_mb\n", "\n", "print(f\"Current memory usage:\")\n", "print(f\"Customers: {customers_memory_mb:.2f} MB\")\n", "print(f\"Transactions: {transactions_memory_mb:.2f} MB\")\n", "print(f\"Sample: {sample_memory_mb:.2f} MB\")\n", "print(f\"Total: {total_memory_mb:.2f} MB\")\n", "\n", "# Clean up variables\n", "del customers_df, transactions_df, sample_transactions_df\n", "del transaction_files, sample_transaction_file\n", "del new_customer_types, transaction_types\n", "del customer_pk_check, transaction_pk_check\n", "del old_mem_usage, new_mem_usage, city_pop_recommended_type\n", "\n", "gc.collect()\n", "print(f\"\\nFreed {total_memory_mb:.2f} MB of memory\")"]}, {"cell_type": "code", "execution_count": null, "id": "073eb87b", "metadata": {}, "outputs": [], "source": ["end_time = time.time()\n", "execution_time = end_time - start_time  # in seconds\n", "print(f\"Using gpu: {GPU_ACCEL}, using modin: {MODIN_AVAILABLE}\")\n", "print(f\"Execution time: {execution_time:.2f} seconds\")"]}, {"cell_type": "markdown", "id": "6561f070", "metadata": {}, "source": ["Using gpu: False, using modin: True\n", "Execution time: 52.14 seconds\n", "\n", "\n", "Using gpu: False, using modin: False\n", "Execution time: 12.63 seconds\n"]}], "metadata": {"kernelspec": {"display_name": "card-transactions-analysis (3.13.5)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}