import time
start_time = time.time()

# Optional GPU acceleration: enable cuDF's pandas accelerator if available (comment out if no CUDA)
# https://developer.nvidia.com/blog/7-drop-in-replacements-to-instantly-speed-up-your-python-data-science-workflows/

# Importing custom functions 
import useful_functions as uf

try:
    get_ipython().run_line_magic('load_ext', 'cudf.pandas')
    GPU_ACCEL = True
except Exception:
    GPU_ACCEL = False
if GPU_ACCEL:
    print("GPU acceleration enabled")
    import cupy as cp, cudf
    print("cuDF:", cudf.__version__, " | CuPy:", cp.__version__)
    n = cp.cuda.runtime.getDeviceCount()
    print("GPU count:", n)
    for i in range(n):
        p = cp.cuda.runtime.getDeviceProperties(i)
        print(f"[{i}] {p['name'].decode()} - {p['totalGlobalMem']//(1024**3)} GB")
else:
    print("GPU acceleration disabled")    
    try:
        import modin.pandas as pd
        MODIN_AVAILABLE = True
        print("Modin available, using Modin with Dask backend.")
        uf.configure_modin_settings()
    except ImportError:
        import pandas as pd
        MODIN_AVAILABLE = False
        print("Modin not available, using pandas.")



print("Libraries imported successfully")

import os
CUSTOMERS_FILE = 'data/customers.csv'
if not os.path.exists(CUSTOMERS_FILE):
    print(f"File not found: {CUSTOMERS_FILE}")
else:
    try:
        customers_df = pd.read_csv(CUSTOMERS_FILE, delimiter='|')
        uf.show_df_info(customers_df)
    except Exception as e:
        print(f"Error loading {CUSTOMERS_FILE}: {e}")

# Splitting the profile column into multiple columns
# https://pandas.pydata.org/docs/reference/api/pandas.Series.str.rsplit.html

profile_parts = customers_df['profile'].str.rsplit('_', n=2, expand=True)
profile_parts.head()

# Leveraging the information from the profile column to create new columns(Feature Engineering)

customers_df['pop_group'] = profile_parts[0]
customers_df['location'] = profile_parts[2].str.split('.').str[0] # Remove .json extension

# Drop the original profile column
customers_df.drop(columns=['profile'], inplace=True)

del profile_parts # Free memory

customers_df.head()

# Testing AI functions to optimize the dataframe memory usage (will use custom approach anyway)
customers_df, optimization_success = uf.test_ai_optimization_safe(customers_df, "customers_df")

if optimization_success:
    print("✅ AI optimization completed successfully")
else:
    print("⚠️ AI optimization failed, using original DataFrame")

# Playing with the AI functions to optimize the dataframe memory usage(will use mines anyway)
df_dict_types = uf.analyze_dataframe_for_optimization(customers_df)
before_df = customers_df.copy()
after_df = uf.optimize_df_types(customers_df, df_dict_types)

uf.print_optimization_report(before_df, after_df, df_dict_types)

# Memory cleanup
import gc

del before_df, after_df, df_dict_types
gc.collect(); # Force garbage collection, otherwise memory will not be released immediately
# The ; at the end avoids printing the output of the last command

# Display unique values in columns that might be used as categorical
customers_df[['gender', 'state', 'job', 'pop_group','location']].nunique()

# Check what type city_pop should use
city_pop_recommended_type = uf.get_safe_int_type(customers_df['city_pop'])
print(f"Recommended type for city_pop: {city_pop_recommended_type}")

# Before optimization
print("Before optimization:")
old_mem_usage = customers_df.memory_usage(deep=True).sum() / 1024
customers_df.info(memory_usage='deep')

# Apply optimization
new_customer_types = {
    'category': ['gender', 'state', 'pop_group', 'location'],
    'string': ['ssn', 'cc_num', 'first', 'last', 'street', 'city', 'dob', 'job', 'zip', 'acct_num'],
    city_pop_recommended_type: ['city_pop'],
    'float32': ['lat', 'long'] # Acceptable loss for fraud detection. It might need to be float64 for other applications
}
# Replacing customers_df with the optimized version to save memory
customers_df = uf.optimize_df_types(customers_df, new_customer_types)

# After optimization  

new_mem_usage = customers_df.memory_usage(deep=True).sum() / 1024
print("\nAfter optimization:")
customers_df.info(memory_usage='deep')

# Compare memory usage

print(f"Original memory usage: {old_mem_usage:,.2f} KB, optimized memory usage: {new_mem_usage:,.2f} KB")

print(f"\nMemory usage reduction: {100*(old_mem_usage - new_mem_usage)/old_mem_usage:.2f}%")

# The transaction data is split across multiple files, so we need to load them all and concatenate them into a single DataFrame
transaction_files = uf.get_files_dir('data', file_mask='*.csv')
transaction_files = [f for f in transaction_files if 'customers.csv' not in f] # Exclude customers.csv

sample_transaction_file = transaction_files[0]
sample_transactions_df = pd.read_csv(sample_transaction_file, delimiter='|')
uf.show_df_info(sample_transactions_df)

sample_transactions_df.dtypes

# Working with the transaction data 
# Optimizing types
transaction_types = {
    'category': ['category'],
    'uint32': ['unix_time'], # uint32 max value is 2,147,483,647, which is more than enough for our purposes(until 2038, at least). 
    'float32': ['amt', 'merch_lat', 'merch_long'],
    'uint8': ['is_fraud'],
    'string': ['ssn', 'trans_num', 'trans_date', 'trans_time', 'merchant']
}
# Process and merge all transaction files
transactions_df = uf.process_and_merge_files(transaction_files, transaction_types)

# Display the final merged DataFrame
uf.show_df_info(transactions_df)

help(uf.get_files_dir)

# 1. Validate primary key candidates
customer_pk_check = uf.check_primary_key_candidates(customers_df, ['ssn', 'cc_num'])
transaction_pk_check = uf.check_primary_key_candidates(transactions_df, ['trans_num'])

print("Customer Primary Key Analysis:")
uf.display_primary_key_analysis( customer_pk_check)
print("\nTransaction Primary Key Analysis:")
uf.display_primary_key_analysis( transaction_pk_check)

# Create database
# Remove the comment to create the database
#uf.create_fraud_detection_db(customers_df, transactions_df)

# Memory cleanup
import gc

# Show current memory usage
customers_memory_mb = customers_df.memory_usage(deep=True).sum() / (1024 * 1024)
transactions_memory_mb = transactions_df.memory_usage(deep=True).sum() / (1024 * 1024)
sample_memory_mb = sample_transactions_df.memory_usage(deep=True).sum() / (1024 * 1024)
total_memory_mb = customers_memory_mb + transactions_memory_mb + sample_memory_mb

print(f"Current memory usage:")
print(f"Customers: {customers_memory_mb:.2f} MB")
print(f"Transactions: {transactions_memory_mb:.2f} MB")
print(f"Sample: {sample_memory_mb:.2f} MB")
print(f"Total: {total_memory_mb:.2f} MB")

# Clean up variables
del customers_df, transactions_df, sample_transactions_df
del transaction_files, sample_transaction_file
del new_customer_types, transaction_types
del customer_pk_check, transaction_pk_check
del old_mem_usage, new_mem_usage, city_pop_recommended_type

gc.collect()
print(f"\nFreed {total_memory_mb:.2f} MB of memory")

end_time = time.time()
execution_time = end_time - start_time  # in seconds
print(f"Using gpu: {GPU_ACCEL}, using modin: {MODIN_AVAILABLE}")
print(f"Execution time: {execution_time:.2f} seconds")