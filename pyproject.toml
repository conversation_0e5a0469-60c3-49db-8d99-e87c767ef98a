[project]
name = "card-transactions-analysis"
version = "0.1.0"
description = "Credit Card transactions analysis for fraud detection and customer segmentation."
readme = "README.md"
requires-python = ">=3.13
dependencies = [
    "ipykernel>=6.29.5",
    "ipython>=9.3.0",
    "matplotlib>=3.10.3",
    "nbformat>=5.10.4",
    "numpy>=2.3.1",
    "pandas>=2.3.0",
    "plotly>=6.2.0",
    "seaborn>=0.13.2",
]
