# Use the new bindings:
try:
    import cupy
except ImportError:
    print("CuPy is not installed. Please install it with 'pip install cupy'.")
    exit(1)

try:
    from cupy.cuda.bindings import runtime
except ImportError:
    print("cupy.cuda.bindings not available. Please check your CuPy installation.")
    exit(1)
try:    
    props = runtime.getDeviceProperties(0)
    if 'name' in props:
        print("GPU name:", props['name'])
    else:
        print("GPU name not found in properties.")
    if 'name' in props:
        print("GPU name:", props['name'])
    else:
        print("GPU name not found in properties.")
except AttributeError as e:
    print("No CUDA-capable GPU detected or API error:", e)
except (ImportError, AttributeError) as e:
    print("No GPU or library not available:", e)
except ImportError as e:
    print("CuPy or CUDA library not available:", e)
except AttributeError as e:
    print("No CUDA-capable GPU detected or API error:", e)
except Exception as e:
    print("An unexpected error occurred:", e)


