{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Initial Exploration\n", "\n", "### Purpose\n", "- Create a complete data dictionary describing all variables\n", "- Provide a brief data overview"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully\n"]}], "source": ["# Import libraries\n", "import pandas as pd\n", "import os\n", "import useful_functions as uf\n", "\n", "print(\"Libraries imported successfully\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. Data Loading and Initial Exploration"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "First few rows of the DataFrame:\n", "           ssn               cc_num    first       last gender  \\\n", "0  ***********        4218196001337    <PERSON>   \n", "1  ***********  4351161559407816183   <PERSON>   \n", "2  ***********        4192832764832  <PERSON>   \n", "3  ***********     4238849696532874  <PERSON>   \n", "4  ***********     4514627048281480     <PERSON>   \n", "\n", "                       street           city state    zip      lat      long  \\\n", "0        863 Lawrence Valleys  Staten Island    NY  10302  40.6306  -74.1379   \n", "1  310 Kendra Common Apt. 164        Peabody    MA   1960  42.5326  -70.9612   \n", "2            05641 Robin Port       Waukomis    OK  73773  36.2781  -97.8996   \n", "3      26916 Carlson Mountain    Los Angeles    CA  90019  34.0482 -118.3343   \n", "4             809 Burns Creek         Austin    TX  78727  30.4254  -97.7195   \n", "\n", "   city_pop                                    job         dob      acct_num  \\\n", "0    468730                  Accounting technician  1982-10-03  ************   \n", "1     50944                     Professor Emeritus  1994-06-07  ************   \n", "2      1744   International aid/development worker  1934-05-30  ************   \n", "3   2383912                    Seismic interpreter  1991-12-26  ************   \n", "4    940359  Medical laboratory scientific officer  1998-05-22  ************   \n", "\n", "                         profile  \n", "0  adults_2550_female_urban.json  \n", "1  adults_2550_female_urban.json  \n", "2  adults_50up_female_rural.json  \n", "3    adults_2550_male_urban.json  \n", "4  adults_2550_female_urban.json  \n", "\n", "DataFrame columns:\n", "['ssn', 'cc_num', 'first', 'last', 'gender', 'street', 'city', 'state', 'zip', 'lat', 'long', 'city_pop', 'job', 'dob', 'acct_num', 'profile']\n", "\n", "DataFrame shape: (1010, 16)\n", "\n", "DataFrame info:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 1010 entries, 0 to 1009\n", "Data columns (total 16 columns):\n", " #   Column    Non-Null Count  Dtype  \n", "---  ------    --------------  -----  \n", " 0   ssn       1010 non-null   object \n", " 1   cc_num    1010 non-null   int64  \n", " 2   first     1010 non-null   object \n", " 3   last      1010 non-null   object \n", " 4   gender    1010 non-null   object \n", " 5   street    1010 non-null   object \n", " 6   city      1010 non-null   object \n", " 7   state     1010 non-null   object \n", " 8   zip       1010 non-null   int64  \n", " 9   lat       1010 non-null   float64\n", " 10  long      1010 non-null   float64\n", " 11  city_pop  1010 non-null   int64  \n", " 12  job       1010 non-null   object \n", " 13  dob       1010 non-null   object \n", " 14  acct_num  1010 non-null   int64  \n", " 15  profile   1010 non-null   object \n", "dtypes: float64(2), int64(4), object(10)\n", "memory usage: 645.4 KB\n", "None\n"]}], "source": ["# Load customer data(using relative path)\n", "customers_df = pd.read_csv('data/customers.csv', delimiter='|')\n", "uf.show_df_info(customers_df)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of transaction files: 53\n", "\n", "Transaction files:\n", "1. adults_2550_female_rural_0000-0201.csv\n", "2. adults_2550_female_rural_0404-0605.csv\n", "3. adults_2550_female_rural_0606-0807.csv\n", "4. adults_2550_female_rural_0808-1009.csv\n", "5. adults_2550_female_urban_0000-0201.csv\n", "... and 48 more files\n"]}], "source": ["# The transaction data is split across multiple files, so we need to load them all and concatenate them into a single DataFrame\n", "# The following function is OS agnostic. It works with 'data', 'data/' and 'data\\\\'.\n", "transaction_files = uf.get_files_dir('data', file_mask='*.csv')\n", "transaction_files = [f for f in transaction_files if 'customers.csv' not in f] # Exclude customers.csv\n", "\n", "print(f\"Number of transaction files: {len(transaction_files)}\")\n", "print(\"\\nTransaction files:\")\n", "for i, file in enumerate(transaction_files[:5]):  # Show first 5\n", "    print(f\"{i+1}. {os.path.basename(file)}\")\n", "if len(transaction_files) > 5:\n", "    print(f\"... and {len(transaction_files) - 5} more files\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "First few rows of the DataFrame:\n", "           ssn                         trans_num  trans_date trans_time  \\\n", "0  ***********  87a777b822486a650c6323a7bb15b471  2023-12-21   00:28:10   \n", "1  ***********  29064f0b63a35ed0f2226eccc24b6c06  2023-12-21   00:26:42   \n", "2  ***********  37a4019328a40f02d7f6c4ffffc06f5f  2023-12-21   00:16:01   \n", "3  ***********  42a3aa638e84935ce525384cbff28753  2023-12-21   02:22:31   \n", "4  ***********  5a788925818de8f0e5ad09b3d30c0cfd  2023-12-21   00:50:39   \n", "\n", "    unix_time       category     amt  is_fraud  \\\n", "0  1703136490       misc_pos    8.05         1   \n", "1  1703136402  gas_transport  361.70         1   \n", "2  1703135761       misc_pos  345.33         1   \n", "3  1703143351       misc_pos  817.02         1   \n", "4  1703137839    grocery_pos  777.19         1   \n", "\n", "                         merchant  merch_lat  merch_long  \n", "0             fraud_<PERSON><PERSON><PERSON>-Upton  41.219555  -73.327258  \n", "1               fraud_Huels-<PERSON>  40.418529  -73.293731  \n", "2         fraud_<PERSON><PERSON><PERSON><PERSON>-<PERSON>ask<PERSON><PERSON>  42.278211  -74.450847  \n", "3               fraud_Haley Group  42.106140  -72.768034  \n", "4  fraud_<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>  40.361006  -73.579606  \n", "\n", "DataFrame columns:\n", "['ssn', 'trans_num', 'trans_date', 'trans_time', 'unix_time', 'category', 'amt', 'is_fraud', 'merchant', 'merch_lat', 'merch_long']\n", "\n", "DataFrame shape: (18055, 11)\n", "\n", "DataFrame info:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 18055 entries, 0 to 18054\n", "Data columns (total 11 columns):\n", " #   Column      Non-Null Count  Dtype  \n", "---  ------      --------------  -----  \n", " 0   ssn         18055 non-null  object \n", " 1   trans_num   18055 non-null  object \n", " 2   trans_date  18055 non-null  object \n", " 3   trans_time  18055 non-null  object \n", " 4   unix_time   18055 non-null  int64  \n", " 5   category    18055 non-null  object \n", " 6   amt         18055 non-null  float64\n", " 7   is_fraud    18055 non-null  int64  \n", " 8   merchant    18055 non-null  object \n", " 9   merch_lat   18055 non-null  float64\n", " 10  merch_long  18055 non-null  float64\n", "dtypes: float64(3), int64(2), object(6)\n", "memory usage: 7.4 MB\n", "None\n"]}], "source": ["# Load a sample transaction file to understand structure\n", "sample_transaction_file = transaction_files[0]\n", "sample_transactions_df = pd.read_csv(sample_transaction_file, delimiter='|')\n", "\n", "uf.show_df_info(sample_transactions_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. Data Dictionary Generation"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Customer Data Variables:\n", "- **ssn**: Social Security Number - Unique customer identifier\n", "- **cc_num**: Credit Card Number - Customer's credit card number\n", "- **first**: First Name - Customer's first name\n", "- **last**: Last Name - Customer's last name\n", "- **gender**: Gender - Customer's gender (M/F)\n", "- **street**: Street Address - Customer's street address\n", "- **city**: City - Customer's city of residence\n", "- **state**: State - Customer's state of residence\n", "- **zip**: ZIP Code - Customer's postal code\n", "- **lat**: Latitude - Geographic latitude of customer address\n", "- **long**: Longitude - Geographic longitude of customer address\n", "- **city_pop**: City Population - Population of customer's city\n", "- **job**: Job Title - Customer's occupation\n", "- **dob**: Date of Birth - Customer's birth date\n", "- **acct_num**: Account Number - Customer's account number\n", "- **profile**: Customer Profile - Demographic profile assignment\n", "\n", "Transaction Data Variables:\n", "- **ssn**: Social Security Number - Links to customer data\n", "- **trans_num**: Transaction Number - Unique transaction identifier\n", "- **trans_date**: Transaction Date - Date of transaction\n", "- **trans_time**: Transaction Time - Time of transaction\n", "- **category**: Transaction Category - Type of purchase/transaction\n", "- **amt**: Amount - Transaction amount in USD\n", "- **is_fraud**: Fraud Flag - Binary indicator (0=legitimate, 1=fraudulent)\n", "- **merchant**: Merchant Name - Name of the merchant/business\n", "- **merch_lat**: Merchant Latitude - Geographic latitude of merchant\n", "- **merch_long**: Merchant Longitude - Geographic longitude of merchant\n"]}], "source": ["# Generate comprehensive data dictionary\n", "def generate_data_dictionary():\n", "    \"\"\"\n", "    Generate a comprehensive data dictionary for both customer and transaction data\n", "    \"\"\"\n", "    \n", "    # Customer data dictionary\n", "    customer_dict = {\n", "        'ssn': 'Social Security Number - Unique customer identifier',\n", "        'cc_num': 'Credit Card Number - Customer\\'s credit card number',\n", "        'first': 'First Name - Customer\\'s first name',\n", "        'last': 'Last Name - Customer\\'s last name', \n", "        'gender': 'Gender - Customer\\'s gender (M/F)',\n", "        'street': 'Street Address - Customer\\'s street address',\n", "        'city': 'City - Customer\\'s city of residence',\n", "        'state': 'State - Customer\\'s state of residence',\n", "        'zip': 'ZIP Code - Customer\\'s postal code',\n", "        'lat': 'Latitude - Geographic latitude of customer address',\n", "        'long': 'Longitude - Geographic longitude of customer address',\n", "        'city_pop': 'City Population - Population of customer\\'s city',\n", "        'job': 'Job Title - Customer\\'s occupation',\n", "        'dob': 'Date of Birth - Customer\\'s birth date',\n", "        'acct_num': 'Account Number - Customer\\'s account number',\n", "        'profile': 'Customer Profile - Demographic profile assignment'\n", "    }\n", "    \n", "    # Transaction data dictionary\n", "    transaction_dict = {\n", "        'ssn': 'Social Security Number - Links to customer data',\n", "        'trans_num': 'Transaction Number - Unique transaction identifier',\n", "        'trans_date': 'Transaction Date - Date of transaction',\n", "        'trans_time': 'Transaction Time - Time of transaction', \n", "        'category': 'Transaction Category - Type of purchase/transaction',\n", "        'amt': 'Amount - Transaction amount in USD',\n", "        'is_fraud': 'Fraud Flag - Binary indicator (0=legitimate, 1=fraudulent)',\n", "        'merchant': 'Merchant Name - Name of the merchant/business',\n", "        'merch_lat': 'Merchant Latitude - Geographic latitude of merchant',\n", "        'merch_long': 'Merchant Longitude - Geographic longitude of merchant'\n", "    }\n", "    \n", "    return customer_dict, transaction_dict\n", "\n", "customer_dict, transaction_dict = generate_data_dictionary()\n", "\n", "print(\"\\nCustomer Data Variables:\")\n", "for col, desc in customer_dict.items():\n", "    if col in customers_df.columns:\n", "        print(f\"- **{col}**: {desc}\")\n", "\n", "print(\"\\nTransaction Data Variables:\")\n", "for col, desc in transaction_dict.items():\n", "    if col in sample_transactions_df.columns:\n", "        print(f\"- **{col}**: {desc}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Output from previous cell\n", "\n", "### Customer Data Variables:\n", "- **ssn**: Social Security Number - Unique customer identifier\n", "- **cc_num**: Credit Card Number - Customer's credit card number\n", "- **first**: First Name - Customer's first name\n", "- **last**: Last Name - Customer's last name\n", "- **gender**: Gender - Customer's gender (M/F)\n", "- **street**: Street Address - Customer's street address\n", "- **city**: City - Customer's city of residence\n", "- **state**: State - Customer's state of residence\n", "- **zip**: ZIP Code - Customer's postal code\n", "- **lat**: Latitude - Geographic latitude of customer address\n", "- **long**: Longitude - Geographic longitude of customer address\n", "- **city_pop**: City Population - Population of customer's city\n", "- **job**: Job Title - Customer's occupation\n", "- **dob**: Date of Birth - Customer's birth date\n", "- **acct_num**: Account Number - Customer's account number\n", "- **profile**: Customer Profile - Demographic profile assignment\n", "\n", "### Transaction Data Variables:\n", "- **ssn**: Social Security Number - Links to customer data\n", "- **trans_num**: Transaction Number - Unique transaction identifier\n", "- **trans_date**: Transaction Date - Date of transaction\n", "- **trans_time**: Transaction Time - Time of transaction\n", "- **category**: Transaction Category - Type of purchase/transaction\n", "- **amt**: Amount - Transaction amount in USD\n", "- **is_fraud**: Fraud Flag - Binary indicator (0=legitimate, 1=fraudulent)\n", "- **merchant**: Merchant Name - Name of the merchant/business\n", "- **merch_lat**: Merchant Latitude - Geographic latitude of merchant\n", "- **merch_long**: Merchant Longitude - Geographic longitude of merchant"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. <PERSON> Summary"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Calculating total transactions across all files...\n", "Done!\n"]}], "source": ["# Calculate total dataset statistics\n", "total_customers = len(customers_df)\n", "total_files = len(transaction_files)\n", "\n", "# Calculate total transactions across all files\n", "total_transactions = 0\n", "file_sizes = []\n", "\n", "print(\"Calculating total transactions across all files...\")\n", "for file in transaction_files:\n", "    try:\n", "        df = pd.read_csv(file, delimiter='|')\n", "        total_transactions += len(df)\n", "        file_sizes.append(len(df))\n", "    except Exception as e:\n", "        print(f\"Error reading {file}: {e}\")\n", "        continue\n", "print('Done!')\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Dataset Overview:\n", "\n", "Total Customers: 1,010\n", "\n", "Total Transaction Files: 53\n", "Total Transactions: 4,740,009\n", "Average Transactions per File: 89,434\n", "Largest File: 322,536 transactions\n", "Smallest File: 2,011 transactions\n"]}], "source": ["print(\"\\nDataset Overview:\\n\")\n", "print(f\"Total Customers: {total_customers:,}\\n\")\n", "print(f\"Total Transaction Files: {total_files}\")\n", "print(f\"Total Transactions: {total_transactions:,}\")\n", "print(f\"Average Transactions per File: {sum(file_sizes) / len(file_sizes):,.0f}\")\n", "print(f\"Largest File: {max(file_sizes):,} transactions\")\n", "print(f\"Smallest File: {min(file_sizes):,} transactions\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Data Quality Assessment"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data Quality Assessment:\n", "\n", "Customer Data Missing Values:\n", "  No missing values found in customer data\n", "\n", "Transaction Data Missing Values (from sample):\n", "  No missing values found in sample transaction data\n"]}], "source": ["# Check for missing values and data quality issues\n", "print(\"Data Quality Assessment:\")\n", "\n", "print(\"\\nCustomer Data Missing Values:\")\n", "customer_missing = uf.get_missing_values(customers_df)\n", "for col, missing in customer_missing.items():\n", "    if missing > 0:\n", "        print(f\"  {col}: {missing} ({missing/len(customers_df)*100:.1f}%)\")\n", "    \n", "if customer_missing.sum() == 0:\n", "    print(\"  No missing values found in customer data\")\n", "\n", "print(\"\\nTransaction Data Missing Values (from sample):\")\n", "transaction_missing = uf.get_missing_values(sample_transactions_df)\n", "for col, missing in transaction_missing.items():\n", "    if missing > 0:\n", "        print(f\"  {col}: {missing} ({missing/len(sample_transactions_df)*100:.1f}%)\")\n", "        \n", "if transaction_missing.sum() == 0:\n", "    print(\"  No missing values found in sample transaction data\")"]}], "metadata": {"kernelspec": {"display_name": "card-transactions-analysis", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}